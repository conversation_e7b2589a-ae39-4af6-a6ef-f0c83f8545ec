1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ppwa.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions untuk PPWA -->
12    <!-- Location permissions -->
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:6:5-81
13-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:6:22-78
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:7:5-79
14-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:7:22-76
15
16    <uses-feature
16-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:8:5-87
17        android:name="android.hardware.location"
17-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:8:19-59
18        android:required="false" />
18-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:8:60-84
19    <uses-feature
19-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:9:5-91
20        android:name="android.hardware.location.gps"
20-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:9:19-63
21        android:required="false" />
21-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:9:64-88
22    <uses-feature
22-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:10:5-95
23        android:name="android.hardware.location.network"
23-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:10:19-67
24        android:required="false" />
24-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:10:68-92
25
26    <!-- Network permissions -->
27    <uses-permission android:name="android.permission.INTERNET" />
27-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:13:5-67
27-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:13:22-64
28    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
28-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:14:5-79
28-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:14:22-76
29    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
29-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:15:5-76
29-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:15:22-73
30
31    <!-- Device information permissions -->
32    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
32-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:18:5-75
32-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:18:22-72
33
34    <!-- Storage permissions untuk offline data -->
35    <uses-permission
35-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:21:5-22:51
36        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
36-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:21:22-78
37        android:maxSdkVersion="28" />
37-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:22:22-48
38    <uses-permission
38-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:23:5-24:51
39        android:name="android.permission.READ_EXTERNAL_STORAGE"
39-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:23:22-77
40        android:maxSdkVersion="32" />
40-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:24:22-48
41
42    <!-- Notification permissions -->
43    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
43-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:27:5-77
43-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:27:22-74
44    <uses-permission android:name="android.permission.VIBRATE" />
44-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:28:5-66
44-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:28:22-63
45
46    <!-- Wake lock untuk background sync -->
47    <uses-permission android:name="android.permission.WAKE_LOCK" />
47-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:31:5-68
47-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:31:22-65
48
49    <!-- Foreground service untuk background sync -->
50    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
50-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:34:5-77
50-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:34:22-74
51
52    <!-- Permissions -->
53
54    <uses-permission android:name="android.permission.INTERNET" />
54-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:13:5-67
54-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:13:22-64
55
56    <permission
56-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
57        android:name="com.ppwa.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="com.ppwa.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
61
62    <application
62-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:36:5-68:19
63        android:allowBackup="true"
63-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:37:9-35
64        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
64-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
65        android:debuggable="true"
66        android:extractNativeLibs="false"
67        android:icon="@mipmap/ic_launcher"
67-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:38:9-43
68        android:label="@string/app_name"
68-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:39:9-41
69        android:roundIcon="@mipmap/ic_launcher_round"
69-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:40:9-54
70        android:supportsRtl="true"
70-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:41:9-35
71        android:testOnly="true"
72        android:theme="@style/AppTheme"
72-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:42:9-40
73        android:usesCleartextTraffic="true" >
73-->[:capacitor-cordova-android-plugins] C:\xampp\htdocs\ppwa\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-53
74        <activity
74-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:44:9-57:20
75            android:name="com.ppwa.app.MainActivity"
75-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:46:13-41
76            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
76-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:45:13-140
77            android:exported="true"
77-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:50:13-36
78            android:label="@string/title_activity_main"
78-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:47:13-56
79            android:launchMode="singleTask"
79-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:49:13-44
80            android:theme="@style/AppTheme.NoActionBarLaunch" >
80-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:48:13-62
81            <intent-filter>
81-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:52:13-55:29
82                <action android:name="android.intent.action.MAIN" />
82-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:53:17-69
82-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:53:25-66
83
84                <category android:name="android.intent.category.LAUNCHER" />
84-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:54:17-77
84-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:54:27-74
85            </intent-filter>
86        </activity>
87
88        <provider
89            android:name="androidx.core.content.FileProvider"
89-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:60:13-62
90            android:authorities="com.ppwa.app.fileprovider"
90-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:61:13-64
91            android:exported="false"
91-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:62:13-37
92            android:grantUriPermissions="true" >
92-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:63:13-47
93            <meta-data
93-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:64:13-66:64
94                android:name="android.support.FILE_PROVIDER_PATHS"
94-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:65:17-67
95                android:resource="@xml/file_paths" />
95-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:66:17-51
96        </provider>
97
98        <activity
98-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
99            android:name="com.google.android.gms.common.api.GoogleApiActivity"
99-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
100            android:exported="false"
100-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
101            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
101-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
102
103        <provider
103-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
104            android:name="androidx.startup.InitializationProvider"
104-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
105            android:authorities="com.ppwa.app.androidx-startup"
105-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
106            android:exported="false" >
106-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
107            <meta-data
107-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.emoji2.text.EmojiCompatInitializer"
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
109                android:value="androidx.startup" />
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
110            <meta-data
110-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
111-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
112                android:value="androidx.startup" />
112-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
115                android:value="androidx.startup" />
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
116        </provider>
117
118        <meta-data
118-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
119            android:name="com.google.android.gms.version"
119-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
120            android:value="@integer/google_play_services_version" />
120-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
121
122        <receiver
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
123            android:name="androidx.profileinstaller.ProfileInstallReceiver"
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
124            android:directBootAware="false"
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
125            android:enabled="true"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
126            android:exported="true"
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
127            android:permission="android.permission.DUMP" >
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
128            <intent-filter>
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
129                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
130            </intent-filter>
131            <intent-filter>
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
132                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
133            </intent-filter>
134            <intent-filter>
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
135                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
136            </intent-filter>
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
138                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
139            </intent-filter>
140        </receiver>
141    </application>
142
143</manifest>
