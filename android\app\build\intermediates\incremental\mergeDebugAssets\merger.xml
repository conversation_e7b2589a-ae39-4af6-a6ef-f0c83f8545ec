<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":capacitor-cordova-android-plugins" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\android\capacitor-cordova-android-plugins\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="native-bridge.js" path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out\native-bridge.js"/></source></dataSet><dataSet config=":capacitor-geolocation" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\geolocation\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-device" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\device\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\app\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\android\app\src\main\assets"><file name="capacitor.config.json" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\capacitor.config.json"/><file name="capacitor.plugins.json" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\capacitor.plugins.json"/><file name="public/assets/index-BfzEqH31.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\index-BfzEqH31.js"/><file name="public/assets/index-DuaR1jdB.css" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\index-DuaR1jdB.css"/><file name="public/assets/web-C4gwNXKv.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\web-C4gwNXKv.js"/><file name="public/assets/web-D0qtDF_9.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\web-D0qtDF_9.js"/><file name="public/assets/web-jah5Na97.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\web-jah5Na97.js"/><file name="public/browserconfig.xml" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\browserconfig.xml"/><file name="public/cordova.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\cordova.js"/><file name="public/cordova_plugins.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\cordova_plugins.js"/><file name="public/index.html" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\index.html"/><file name="public/manifest.json" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\manifest.json"/><file name="public/sw.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\sw.js"/><file name="public/vite.svg" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\vite.svg"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>